import React, { useState } from 'react';
import { 
  FiX, FiEdit3, FiTrash2, FiDownload, FiMove, FiTag, 
  Fi<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lertTriangle 
} from 'react-icons/fi';
import HierarchicalSelector from './forms/HierarchicalSelector';
import './BulkOperationsPanel.css';

const BulkOperationsPanel = ({ selectedPersons, onClose, onAction }) => {
  const [activeOperation, setActiveOperation] = useState(null);
  const [operationData, setOperationData] = useState({});
  const [confirmAction, setConfirmAction] = useState(null);

  const operations = [
    {
      id: 'move',
      title: 'Move to Division/Category',
      icon: FiMove,
      description: 'Change division, category, or subcategory for selected persons',
      color: '#3b82f6'
    },
    {
      id: 'updateNature',
      title: 'Update Nature',
      icon: FiTag,
      description: 'Change the nature classification for selected persons',
      color: '#8b5cf6'
    },
    {
      id: 'export',
      title: 'Export Selected',
      icon: FiDownload,
      description: 'Export selected persons to Excel or CSV',
      color: '#10b981'
    },
    {
      id: 'delete',
      title: 'Delete Selected',
      icon: FiTrash2,
      description: 'Permanently delete selected persons',
      color: '#ef4444',
      dangerous: true
    }
  ];

  const handleOperationSelect = (operation) => {
    setActiveOperation(operation);
    setOperationData({});
  };

  const handleExecute = () => {
    if (activeOperation?.dangerous) {
      setConfirmAction(activeOperation);
    } else {
      executeOperation();
    }
  };

  const executeOperation = () => {
    onAction(activeOperation.id, {
      personIds: selectedPersons,
      ...operationData
    });
    setConfirmAction(null);
    setActiveOperation(null);
  };

  const renderOperationForm = () => {
    if (!activeOperation) return null;

    switch (activeOperation.id) {
      case 'move':
        return (
          <div className="operation-form">
            <h4>Select New Location</h4>
            <HierarchicalSelector
              onSelectionChange={(selection) => 
                setOperationData(selection)
              }
              showLabels={true}
            />
          </div>
        );

      case 'updateNature':
        return (
          <div className="operation-form">
            <h4>Select New Nature</h4>
            <div className="nature-options">
              {[
                { value: 1, label: 'Business', color: '#3b82f6' },
                { value: 2, label: 'Corporate', color: '#ef4444' },
                { value: 3, label: 'Agriculture', color: '#10b981' },
                { value: 4, label: 'Individual', color: '#f59e0b' }
              ].map(nature => (
                <label key={nature.value} className="nature-option">
                  <input
                    type="radio"
                    name="nature"
                    value={nature.value}
                    onChange={(e) => setOperationData({ nature: parseInt(e.target.value) })}
                  />
                  <span 
                    className="nature-badge"
                    style={{ backgroundColor: nature.color }}
                  >
                    {nature.label}
                  </span>
                </label>
              ))}
            </div>
          </div>
        );

      case 'export':
        return (
          <div className="operation-form">
            <h4>Export Options</h4>
            <div className="export-options">
              <label className="option-item">
                <input
                  type="radio"
                  name="format"
                  value="excel"
                  defaultChecked
                  onChange={(e) => setOperationData(prev => ({ ...prev, format: e.target.value }))}
                />
                <span>Excel (.xlsx)</span>
              </label>
              <label className="option-item">
                <input
                  type="radio"
                  name="format"
                  value="csv"
                  onChange={(e) => setOperationData(prev => ({ ...prev, format: e.target.value }))}
                />
                <span>CSV (.csv)</span>
              </label>
            </div>
            
            <div className="field-selection">
              <h5>Include Fields</h5>
              <div className="field-checkboxes">
                {[
                  'name', 'mobile', 'email', 'division', 'category', 
                  'nature', 'firmName', 'address', 'createdAt'
                ].map(field => (
                  <label key={field} className="field-checkbox">
                    <input
                      type="checkbox"
                      defaultChecked
                      onChange={(e) => {
                        const fields = operationData.fields || [];
                        if (e.target.checked) {
                          setOperationData(prev => ({ 
                            ...prev, 
                            fields: [...fields, field] 
                          }));
                        } else {
                          setOperationData(prev => ({ 
                            ...prev, 
                            fields: fields.filter(f => f !== field) 
                          }));
                        }
                      }}
                    />
                    <span>{field.charAt(0).toUpperCase() + field.slice(1)}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 'delete':
        return (
          <div className="operation-form danger">
            <div className="warning-message">
              <FiAlertTriangle className="warning-icon" />
              <div>
                <h4>Confirm Deletion</h4>
                <p>
                  This action will permanently delete {selectedPersons.length} person(s). 
                  This cannot be undone.
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bulk-operations-overlay" onClick={onClose}>
      <div className="bulk-operations-panel" onClick={e => e.stopPropagation()}>
        <div className="panel-header">
          <div className="header-content">
            <FiUsers className="header-icon" />
            <div>
              <h3>Bulk Operations</h3>
              <p>{selectedPersons.length} person(s) selected</p>
            </div>
          </div>
          <button onClick={onClose} className="btn-close">
            <FiX />
          </button>
        </div>

        <div className="panel-body">
          {!activeOperation ? (
            <div className="operations-grid">
              {operations.map(operation => (
                <button
                  key={operation.id}
                  onClick={() => handleOperationSelect(operation)}
                  className={`operation-card ${operation.dangerous ? 'dangerous' : ''}`}
                >
                  <div 
                    className="operation-icon"
                    style={{ backgroundColor: operation.color }}
                  >
                    <operation.icon />
                  </div>
                  <div className="operation-content">
                    <h4>{operation.title}</h4>
                    <p>{operation.description}</p>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="operation-details">
              <div className="operation-header">
                <button 
                  onClick={() => setActiveOperation(null)}
                  className="btn-back"
                >
                  ← Back
                </button>
                <h4>{activeOperation.title}</h4>
              </div>
              
              {renderOperationForm()}
            </div>
          )}
        </div>

        {activeOperation && (
          <div className="panel-footer">
            <button 
              onClick={() => setActiveOperation(null)}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            <button 
              onClick={handleExecute}
              className={`btn ${activeOperation.dangerous ? 'btn-danger' : 'btn-primary'}`}
            >
              {activeOperation.dangerous ? (
                <>
                  <FiTrash2 />
                  Delete {selectedPersons.length} Person(s)
                </>
              ) : (
                <>
                  <FiCheck />
                  Apply Changes
                </>
              )}
            </button>
          </div>
        )}

        {/* Confirmation Modal */}
        {confirmAction && (
          <div className="confirm-overlay">
            <div className="confirm-modal">
              <div className="confirm-header">
                <FiAlertTriangle className="confirm-icon" />
                <h4>Confirm Action</h4>
              </div>
              <div className="confirm-body">
                <p>
                  Are you sure you want to {confirmAction.title.toLowerCase()} 
                  {selectedPersons.length} person(s)? This action cannot be undone.
                </p>
              </div>
              <div className="confirm-footer">
                <button 
                  onClick={() => setConfirmAction(null)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button 
                  onClick={executeOperation}
                  className="btn btn-danger"
                >
                  Yes, {confirmAction.title}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BulkOperationsPanel;
