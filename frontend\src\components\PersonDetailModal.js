import React from 'react';
import {
  FiX, FiEdit3, FiPhone, FiMail, FiMapPin, FiCalendar,
  FiUser, FiBriefcase, FiTag, FiClock
} from 'react-icons/fi';
import './PersonDetailModal.css';

const PersonDetailModal = ({ person, onClose, onEdit }) => {
  if (!person) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNatureColor = (nature) => {
    const colors = {
      1: '#007bff', // Business
      2: '#dc3545', // Corporate
      3: '#28a745', // Agriculture
      4: '#ffc107'  // Individual
    };
    return colors[nature] || '#6c757d';
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="person-detail-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <div className="header-content">
            <div className="person-avatar">
              <FiUser size={24} />
            </div>
            <div className="person-title">
              <h2>{person.name}</h2>
              {person.firmName && (
                <p className="firm-name">
                  <FiBriefcase className="icon" />
                  {person.firmName}
                </p>
              )}
            </div>
          </div>
          <div className="header-actions">
            <button 
              onClick={() => onEdit(person)}
              className="btn btn-primary"
            >
              <FiEdit3 />
              Edit
            </button>
            <button onClick={onClose} className="btn-close">
              <FiX />
            </button>
          </div>
        </div>

        <div className="modal-body">
          <div className="detail-grid">
            {/* Contact Information */}
            <div className="detail-section">
              <h3>
                <FiPhone className="section-icon" />
                Contact Information
              </h3>
              <div className="detail-content">
                <div className="detail-item">
                  <label>Primary Mobile</label>
                  <div className="value">
                    {person.mobileNumber || 'Not provided'}
                  </div>
                </div>
                
                {person.alternateNumbers?.length > 0 && (
                  <div className="detail-item">
                    <label>Alternate Numbers</label>
                    <div className="value">
                      {person.alternateNumbers.join(', ')}
                    </div>
                  </div>
                )}
                
                <div className="detail-item">
                  <label>Primary Email</label>
                  <div className="value">
                    {person.primaryEmailId || 'Not provided'}
                  </div>
                </div>
                
                {person.alternateEmailIds?.length > 0 && (
                  <div className="detail-item">
                    <label>Alternate Emails</label>
                    <div className="value">
                      {person.alternateEmailIds.join(', ')}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Organization */}
            <div className="detail-section">
              <h3>
                <FiMapPin className="section-icon" />
                Organization
              </h3>
              <div className="detail-content">
                <div className="detail-item">
                  <label>Division</label>
                  <div className="value">
                    {person.division?.name || 'Not assigned'}
                  </div>
                </div>
                
                <div className="detail-item">
                  <label>Category</label>
                  <div className="value">
                    {person.category?.name || 'Not assigned'}
                  </div>
                </div>
                
                {person.subCategory && (
                  <div className="detail-item">
                    <label>Sub Category</label>
                    <div className="value">
                      {person.subCategory.name}
                    </div>
                  </div>
                )}
                
                <div className="detail-item">
                  <label>Nature</label>
                  <div className="value">
                    <span 
                      className="nature-badge"
                      style={{ backgroundColor: getNatureColor(person.nature) }}
                    >
                      {person.natureDisplay}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="detail-section">
              <h3>
                <FiTag className="section-icon" />
                Additional Information
              </h3>
              <div className="detail-content">
                {person.address && (
                  <div className="detail-item">
                    <label>Address</label>
                    <div className="value">{person.address}</div>
                  </div>
                )}
                
                {person.notes && (
                  <div className="detail-item">
                    <label>Notes</label>
                    <div className="value">{person.notes}</div>
                  </div>
                )}
              </div>
            </div>

            {/* System Information */}
            <div className="detail-section">
              <h3>
                <FiClock className="section-icon" />
                System Information
              </h3>
              <div className="detail-content">
                <div className="detail-item">
                  <label>Created</label>
                  <div className="value">
                    <FiCalendar className="inline-icon" />
                    {formatDate(person.createdAt)}
                  </div>
                </div>
                
                {person.updatedAt && (
                  <div className="detail-item">
                    <label>Last Updated</label>
                    <div className="value">
                      <FiCalendar className="inline-icon" />
                      {formatDate(person.updatedAt)}
                    </div>
                  </div>
                )}
                
                <div className="detail-item">
                  <label>Person ID</label>
                  <div className="value">#{person.id}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="btn btn-secondary">
            Close
          </button>
          <button 
            onClick={() => onEdit(person)}
            className="btn btn-primary"
          >
            <FiEdit3 />
            Edit Person
          </button>
        </div>
      </div>
    </div>
  );
};

export default PersonDetailModal;
