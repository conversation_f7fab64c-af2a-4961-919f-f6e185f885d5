import React, { useState } from 'react';
import { 
  FiX, FiDownload, FiFileText, FiGrid, FiFilter, 
  FiCalendar, FiCheck, FiSettings 
} from 'react-icons/fi';
import './ExportModal.css';

const ExportModal = ({ onClose, filters, totalCount }) => {
  const [exportConfig, setExportConfig] = useState({
    format: 'excel',
    scope: 'filtered', // 'all', 'filtered', 'selected'
    fields: [
      'name', 'mobile', 'email', 'division', 'category', 
      'nature', 'firmName', 'createdAt'
    ],
    includeHeaders: true,
    dateFormat: 'standard'
  });

  const [isExporting, setIsExporting] = useState(false);

  const availableFields = [
    { key: 'name', label: 'Name', required: true },
    { key: 'firmName', label: 'Firm Name' },
    { key: 'mobile', label: 'Mobile Number' },
    { key: 'email', label: 'Email Address' },
    { key: 'division', label: 'Division' },
    { key: 'category', label: 'Category' },
    { key: 'subCategory', label: 'Sub Category' },
    { key: 'nature', label: 'Nature' },
    { key: 'address', label: 'Address' },
    { key: 'alternateNumbers', label: 'Alternate Numbers' },
    { key: 'alternateEmails', label: 'Alternate Emails' },
    { key: 'createdAt', label: 'Created Date' },
    { key: 'updatedAt', label: 'Updated Date' }
  ];

  const handleFieldToggle = (fieldKey) => {
    const field = availableFields.find(f => f.key === fieldKey);
    if (field?.required) return; // Don't allow toggling required fields

    setExportConfig(prev => ({
      ...prev,
      fields: prev.fields.includes(fieldKey)
        ? prev.fields.filter(f => f !== fieldKey)
        : [...prev.fields, fieldKey]
    }));
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In real implementation, this would call the API
      console.log('Export configuration:', exportConfig);
      console.log('Applied filters:', filters);
      
      // Close modal after successful export
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getEstimatedCount = () => {
    switch (exportConfig.scope) {
      case 'all':
        return totalCount;
      case 'filtered':
        return totalCount; // In real app, this would be the filtered count
      case 'selected':
        return 0; // Would be passed as prop
      default:
        return 0;
    }
  };

  return (
    <div className="export-modal-overlay" onClick={onClose}>
      <div className="export-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <div className="header-content">
            <FiDownload className="header-icon" />
            <div>
              <h3>Export Persons</h3>
              <p>Configure your export settings</p>
            </div>
          </div>
          <button onClick={onClose} className="btn-close">
            <FiX />
          </button>
        </div>

        <div className="modal-body">
          {/* Export Format */}
          <div className="config-section">
            <h4>
              <FiFileText className="section-icon" />
              Export Format
            </h4>
            <div className="format-options">
              <label className="format-option">
                <input
                  type="radio"
                  name="format"
                  value="excel"
                  checked={exportConfig.format === 'excel'}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, format: e.target.value }))}
                />
                <div className="format-card">
                  <FiGrid className="format-icon" />
                  <div>
                    <strong>Excel (.xlsx)</strong>
                    <p>Best for data analysis and formatting</p>
                  </div>
                </div>
              </label>
              
              <label className="format-option">
                <input
                  type="radio"
                  name="format"
                  value="csv"
                  checked={exportConfig.format === 'csv'}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, format: e.target.value }))}
                />
                <div className="format-card">
                  <FiFileText className="format-icon" />
                  <div>
                    <strong>CSV (.csv)</strong>
                    <p>Universal format, works with any spreadsheet app</p>
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* Export Scope */}
          <div className="config-section">
            <h4>
              <FiFilter className="section-icon" />
              Export Scope
            </h4>
            <div className="scope-options">
              <label className="scope-option">
                <input
                  type="radio"
                  name="scope"
                  value="filtered"
                  checked={exportConfig.scope === 'filtered'}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, scope: e.target.value }))}
                />
                <div className="scope-info">
                  <strong>Current Search Results</strong>
                  <p>Export {getEstimatedCount().toLocaleString()} persons matching current filters</p>
                </div>
              </label>
              
              <label className="scope-option">
                <input
                  type="radio"
                  name="scope"
                  value="all"
                  checked={exportConfig.scope === 'all'}
                  onChange={(e) => setExportConfig(prev => ({ ...prev, scope: e.target.value }))}
                />
                <div className="scope-info">
                  <strong>All Persons</strong>
                  <p>Export all {totalCount.toLocaleString()} persons in the database</p>
                </div>
              </label>
            </div>
          </div>

          {/* Field Selection */}
          <div className="config-section">
            <h4>
              <FiSettings className="section-icon" />
              Fields to Include
            </h4>
            <div className="field-grid">
              {availableFields.map(field => (
                <label key={field.key} className="field-option">
                  <input
                    type="checkbox"
                    checked={exportConfig.fields.includes(field.key)}
                    onChange={() => handleFieldToggle(field.key)}
                    disabled={field.required}
                  />
                  <span className={field.required ? 'required' : ''}>
                    {field.label}
                    {field.required && <span className="required-indicator">*</span>}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Additional Options */}
          <div className="config-section">
            <h4>
              <FiCalendar className="section-icon" />
              Additional Options
            </h4>
            <div className="additional-options">
              <label className="option-item">
                <input
                  type="checkbox"
                  checked={exportConfig.includeHeaders}
                  onChange={(e) => setExportConfig(prev => ({ 
                    ...prev, 
                    includeHeaders: e.target.checked 
                  }))}
                />
                <span>Include column headers</span>
              </label>
              
              <div className="date-format-option">
                <label>Date Format:</label>
                <select
                  value={exportConfig.dateFormat}
                  onChange={(e) => setExportConfig(prev => ({ 
                    ...prev, 
                    dateFormat: e.target.value 
                  }))}
                >
                  <option value="standard">MM/DD/YYYY</option>
                  <option value="iso">YYYY-MM-DD</option>
                  <option value="long">Month DD, YYYY</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <div className="export-summary">
            <p>
              Exporting {getEstimatedCount().toLocaleString()} persons 
              with {exportConfig.fields.length} fields 
              as {exportConfig.format.toUpperCase()}
            </p>
          </div>
          
          <div className="footer-actions">
            <button onClick={onClose} className="btn btn-secondary">
              Cancel
            </button>
            <button 
              onClick={handleExport}
              disabled={isExporting}
              className="btn btn-primary"
            >
              {isExporting ? (
                <>
                  <div className="spinner" />
                  Exporting...
                </>
              ) : (
                <>
                  <FiDownload />
                  Export Data
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;
