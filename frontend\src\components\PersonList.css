/* Enhanced PersonList Styles */
.person-list-enhanced {
  padding: 1.5rem;
  background: #f8fafc;
  min-height: 100vh;
}

.person-list {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background-color: #f8f9fa;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-title h2 {
  margin: 0;
  color: #495057;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.hierarchy-filter {
  margin: 1rem 0;
}

.search-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  font-size: 0.875rem;
  color: #6c757d;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.person-table-container {
  overflow-x: auto;
}

.person-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.person-table th {
  background-color: #f8f9fa;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e1e5e9;
  white-space: nowrap;
}

.person-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e1e5e9;
  vertical-align: top;
}

.person-table tbody tr:hover {
  background-color: #f8f9fa;
}

.person-name strong {
  color: #495057;
  font-weight: 600;
}

.firm-name {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.contact-info,
.email-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alternate {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
}

.subcategory {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.nature-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.nature-badge.nature-1 { /* Business */
  background-color: #e7f3ff;
  color: #0056b3;
}

.nature-badge.nature-2 { /* Corporate */
  background-color: #f8d7da;
  color: #721c24;
}

.nature-badge.nature-3 { /* Agriculture */
  background-color: #d4edda;
  color: #155724;
}

.nature-badge.nature-4 { /* Individual */
  background-color: #fff3cd;
  color: #856404;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.btn-action {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.btn-action:hover {
  background-color: #e9ecef;
}

.btn-action.edit:hover {
  background-color: #fff3cd;
}

.btn-action.view:hover {
  background-color: #e7f3ff;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
  font-style: italic;
}

.link-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
}

.link-btn:hover {
  color: #0056b3;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  background-color: white;
  color: #495057;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.pagination-ellipsis {
  padding: 0.5rem 0.25rem;
  color: #6c757d;
}

.alert {
  padding: 1rem 1.25rem;
  margin: 1.5rem;
  border: 1px solid transparent;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.retry-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-btn:hover {
  background-color: #c82333;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  border-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .list-header {
    padding: 1rem;
  }
  
  .header-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: stretch;
  }
  
  .header-actions .btn {
    flex: 1;
  }
  
  .search-filters {
    grid-template-columns: 1fr;
  }
  
  .search-actions {
    justify-content: stretch;
  }
  
  .search-actions .btn {
    flex: 1;
  }
  
  .results-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .person-table {
    font-size: 0.75rem;
  }
  
  .person-table th,
  .person-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .person-table-container {
    font-size: 0.7rem;
  }
  
  .person-table th:nth-child(n+6),
  .person-table td:nth-child(n+6) {
    display: none;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

/* Enhanced Professional Styles */

/* Enhanced Header */
.list-header-enhanced {
  margin-bottom: 2rem;
}

.header-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-title h2 {
  margin: 0;
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
}

.header-icon {
  color: #6366f1;
  font-size: 2rem;
}

.header-actions-enhanced {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #6366f1;
  color: white;
}

.btn-primary:hover {
  background: #5b21b6;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.btn-secondary:hover {
  background: #d1d5db;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.total {
  border-left-color: #6366f1;
}

.stat-card.email {
  border-left-color: #10b981;
}

.stat-card.mobile {
  border-left-color: #f59e0b;
}

.stat-card.recent {
  border-left-color: #ef4444;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.stat-icon {
  padding: 0.5rem;
  border-radius: 6px;
  color: white;
}

.stat-card.total .stat-icon {
  background: #6366f1;
}

.stat-card.email .stat-icon {
  background: #10b981;
}

.stat-card.mobile .stat-icon {
  background: #f59e0b;
}

.stat-card.recent .stat-icon {
  background: #ef4444;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

/* Enhanced Search Section */
.search-section-enhanced {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.search-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.main-search {
  position: relative;
}

.search-input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
}

.search-input-enhanced:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.advanced-filters {
  padding: 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-group-enhanced {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group-enhanced label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.filter-group-enhanced select,
.filter-group-enhanced input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.filter-group-enhanced select:focus,
.filter-group-enhanced input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Enhanced Results Summary */
.results-summary-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.summary-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.results-count {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.selection-count {
  background: #6366f1;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.summary-center {
  display: flex;
  align-items: center;
}

.summary-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-controls {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  background: white;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.view-btn:hover {
  background: #f3f4f6;
}

.view-btn.active {
  background: #6366f1;
  color: white;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.page-size-selector select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Enhanced Table */
.person-table-container-enhanced {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.person-table-enhanced {
  width: 100%;
  border-collapse: collapse;
}

.person-table-enhanced th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.person-table-enhanced th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.person-table-enhanced th.sortable:hover {
  background: #f1f5f9;
}

.person-table-enhanced th .header-icon {
  margin-right: 0.5rem;
  color: #6b7280;
}

.sort-indicator {
  margin-left: 0.5rem;
  color: #6366f1;
  font-weight: bold;
}

.person-table-enhanced td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.person-table-enhanced tr:hover {
  background: #f8fafc;
}

.person-table-enhanced tr.selected {
  background: #eff6ff;
}

.select-column,
.select-cell {
  width: 40px;
  text-align: center;
}

.name-cell .person-name strong {
  color: #1f2937;
  font-weight: 600;
}

.name-cell .firm-name {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.contact-cell .contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.contact-icon {
  color: #6b7280;
  font-size: 0.875rem;
}

.no-contact {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.875rem;
}

.alternate {
  font-size: 0.75rem;
  color: #6b7280;
}

.hierarchy-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.hierarchy-icon {
  color: #6b7280;
  font-size: 0.875rem;
}

.subcategory {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.nature-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: white;
}

.nature-1 { background: #3b82f6; }
.nature-2 { background: #ef4444; }
.nature-3 { background: #10b981; }
.nature-4 { background: #f59e0b; }

.date-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.date-icon {
  font-size: 0.875rem;
}

.actions-cell {
  width: 120px;
}

.action-buttons-enhanced {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.btn-action {
  background: none;
  border: 1px solid #e5e7eb;
  padding: 0.375rem;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-action:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.btn-action.view:hover {
  color: #3b82f6;
  border-color: #3b82f6;
}

.btn-action.edit:hover {
  color: #10b981;
  border-color: #10b981;
}

.btn-action.more:hover {
  color: #6b7280;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* Grid View */
.person-grid-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.person-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.person-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s;
  cursor: pointer;
}

.person-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.person-card.selected {
  border-color: #6366f1;
  background: #eff6ff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-checkbox {
  accent-color: #6366f1;
}

.card-actions {
  display: flex;
  gap: 0.25rem;
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.card-content .firm-name {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.contact-details .contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.card-meta .hierarchy {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .person-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .person-list-enhanced {
    padding: 1rem;
  }

  .header-title-section {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions-enhanced {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .results-summary-enhanced {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .summary-right {
    width: 100%;
    justify-content: space-between;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .person-grid {
    grid-template-columns: 1fr;
  }

  .person-table-enhanced {
    font-size: 0.875rem;
  }

  .person-table-enhanced th,
  .person-table-enhanced td {
    padding: 0.75rem 0.5rem;
  }

  .action-buttons-enhanced {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .person-list-enhanced {
    padding: 0.5rem;
  }

  .header-title h2 {
    font-size: 1.5rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .search-header {
    padding: 1rem;
  }

  .advanced-filters {
    padding: 1rem;
  }

  .results-summary-enhanced {
    padding: 1rem;
  }

  .person-card {
    padding: 1rem;
  }

  /* Hide some table columns on mobile */
  .person-table-enhanced .division-cell,
  .person-table-enhanced .date-cell {
    display: none;
  }

  .person-table-enhanced th:nth-child(5),
  .person-table-enhanced th:nth-child(8) {
    display: none;
  }
}
