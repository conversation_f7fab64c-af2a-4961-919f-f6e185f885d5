import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  FiSearch, FiFilter, FiDownload, FiEdit3, FiEye, FiMoreVertical,
  FiUsers, FiTrendingUp, FiCalendar, FiMapPin, FiPhone, FiMail,
  FiGrid, FiList, FiRefreshCw, FiSettings, FiCheckSquare, FiSquare
} from 'react-icons/fi';
import apiService from '../services/apiService';
import HierarchicalSelector from './forms/HierarchicalSelector';
import PersonDetailModal from './PersonDetailModal';
import BulkOperationsPanel from './BulkOperationsPanel';
import ExportModal from './ExportModal';
import './PersonList.css';

const PersonList = ({ onEditPerson }) => {
  const [persons, setPersons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchFilters, setSearchFilters] = useState({
    name: '',
    mobileNumber: '',
    email: '',
    divisionId: null,
    categoryId: null,
    subCategoryId: null,
    nature: '',
    dateFrom: '',
    dateTo: '',
    hasEmail: null,
    hasMobile: null
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0
  });

  // New state for enhanced features
  const [viewMode, setViewMode] = useState('table'); // 'table', 'grid', 'cards'
  const [selectedPersons, setSelectedPersons] = useState(new Set());
  const [showBulkPanel, setShowBulkPanel] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [sortConfig, setSortConfig] = useState({ field: 'createdAt', direction: 'desc' });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [savedSearches, setSavedSearches] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    withEmail: 0,
    withMobile: 0,
    recentlyAdded: 0
  });

  useEffect(() => {
    loadPersons();
  }, [pagination.page, pagination.pageSize, sortConfig]);

  useEffect(() => {
    loadStats();
  }, []);

  const loadPersons = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchRequest = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        sortBy: sortConfig.field,
        sortDirection: sortConfig.direction,
        includeDivision: true,
        includeCategory: true,
        includeSubCategory: true,
        ...searchFilters
      };

      const response = await apiService.searchPersons(searchRequest);
      setPersons(response.data.persons || []);
      setPagination(prev => ({
        ...prev,
        totalCount: response.data.totalCount || 0,
        totalPages: response.data.totalPages || 0
      }));

      // Update stats
      setStats(prev => ({
        ...prev,
        total: response.data.totalCount || 0
      }));
    } catch (err) {
      console.error('Error loading persons:', err);
      setError('Failed to load persons. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, sortConfig, searchFilters]);

  const loadStats = async () => {
    try {
      const response = await apiService.getPersonStats();
      setStats(response.data);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadPersons();
  };

  const handleFilterChange = (key, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleHierarchyChange = (selection) => {
    setSearchFilters(prev => ({
      ...prev,
      divisionId: selection.divisionId,
      categoryId: selection.categoryId,
      subCategoryId: selection.subCategoryId
    }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = (newPageSize) => {
    setPagination(prev => ({ ...prev, pageSize: newPageSize, page: 1 }));
  };

  const clearFilters = () => {
    setSearchFilters({
      name: '',
      mobileNumber: '',
      email: '',
      divisionId: null,
      categoryId: null,
      subCategoryId: null
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  // New enhanced handlers
  const handleSort = (field) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectPerson = (personId, selected) => {
    setSelectedPersons(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(personId);
      } else {
        newSet.delete(personId);
      }
      return newSet;
    });
  };

  const handleSelectAll = (selected) => {
    if (selected) {
      setSelectedPersons(new Set(persons.map(p => p.id)));
    } else {
      setSelectedPersons(new Set());
    }
  };

  const handleViewPerson = (person) => {
    setSelectedPerson(person);
    setShowDetailModal(true);
  };

  const handleRefresh = () => {
    loadPersons();
    loadStats();
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const handleBulkAction = (action, data) => {
    // Handle bulk operations
    console.log('Bulk action:', action, data);
    setSelectedPersons(new Set());
    setShowBulkPanel(false);
    loadPersons();
  };

  const renderPagination = () => {
    const { page, totalPages } = pagination;
    const pages = [];
    
    // Calculate page range
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="pagination">
        <button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
          className="pagination-btn"
        >
          Previous
        </button>
        
        {startPage > 1 && (
          <>
            <button onClick={() => handlePageChange(1)} className="pagination-btn">1</button>
            {startPage > 2 && <span className="pagination-ellipsis">...</span>}
          </>
        )}
        
        {pages.map(pageNum => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`pagination-btn ${pageNum === page ? 'active' : ''}`}
          >
            {pageNum}
          </button>
        ))}
        
        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && <span className="pagination-ellipsis">...</span>}
            <button onClick={() => handlePageChange(totalPages)} className="pagination-btn">
              {totalPages}
            </button>
          </>
        )}
        
        <button
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages}
          className="pagination-btn"
        >
          Next
        </button>
      </div>
    );
  };

  // Computed values
  const allSelected = persons.length > 0 && selectedPersons.size === persons.length;
  const someSelected = selectedPersons.size > 0 && selectedPersons.size < persons.length;

  return (
    <div className="person-list-enhanced">
      {/* Enhanced Header with Stats */}
      <div className="list-header-enhanced">
        <div className="header-top">
          <div className="header-title-section">
            <h2><FiUsers className="header-icon" /> Person Directory</h2>
            <p className="header-subtitle">Manage and organize your contacts efficiently</p>
          </div>

          <div className="header-actions">
            <button
              onClick={handleRefresh}
              className="btn btn-secondary"
              disabled={loading}
            >
              <FiRefreshCw className={loading ? 'spinning' : ''} />
              Refresh
            </button>
            <button
              onClick={handleExport}
              className="btn btn-outline"
            >
              <FiDownload />
              Export
            </button>
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`btn btn-outline ${showAdvancedFilters ? 'active' : ''}`}
            >
              <FiFilter />
              Advanced Filters
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="stats-row">
          <div className="stat-card">
            <div className="stat-icon">
              <FiUsers />
            </div>
            <div className="stat-content">
              <div className="stat-value">{stats.total.toLocaleString()}</div>
              <div className="stat-label">Total Persons</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <FiMail />
            </div>
            <div className="stat-content">
              <div className="stat-value">{stats.withEmail || 0}</div>
              <div className="stat-label">With Email</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <FiPhone />
            </div>
            <div className="stat-content">
              <div className="stat-value">{stats.withMobile || 0}</div>
              <div className="stat-label">With Mobile</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <FiTrendingUp />
            </div>
            <div className="stat-content">
              <div className="stat-value">{stats.recentlyAdded || 0}</div>
              <div className="stat-label">Recent (7 days)</div>
            </div>
          </div>
        </div>

        {/* Enhanced Search Section */}
        <div className="search-section-enhanced">
          <div className="search-bar">
            <div className="search-input-group">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search by name, mobile, email..."
                value={searchFilters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                className="search-input-main"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            <div className="search-controls">
              <button onClick={handleSearch} className="btn btn-primary">
                <FiSearch />
                Search
              </button>
              <button onClick={clearFilters} className="btn btn-outline">
                Clear
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="advanced-filters">
              <div className="filter-row">
                <div className="filter-group">
                  <label>Mobile Number</label>
                  <input
                    type="text"
                    placeholder="Search by mobile..."
                    value={searchFilters.mobile}
                    onChange={(e) => handleFilterChange('mobile', e.target.value)}
                    className="filter-input"
                  />
                </div>

                <div className="filter-group">
                  <label>Email</label>
                  <input
                    type="text"
                    placeholder="Search by email..."
                    value={searchFilters.email}
                    onChange={(e) => handleFilterChange('email', e.target.value)}
                    className="filter-input"
                  />
                </div>

                <div className="filter-group">
                  <label>Nature</label>
                  <select
                    value={searchFilters.nature}
                    onChange={(e) => handleFilterChange('nature', e.target.value)}
                    className="filter-select"
                  >
                    <option value="">All Natures</option>
                    <option value="1">Business</option>
                    <option value="2">Corporate</option>
                    <option value="3">Agriculture</option>
                    <option value="4">Individual</option>
                  </select>
                </div>
              </div>

              <div className="hierarchy-filter-enhanced">
                <HierarchicalSelector
                  onSelectionChange={handleHierarchyChange}
                  selectedDivisionId={searchFilters.divisionId}
                  selectedCategoryId={searchFilters.categoryId}
                  selectedSubCategoryId={searchFilters.subCategoryId}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Results Summary and Controls */}
      <div className="results-summary-enhanced">
        <div className="summary-left">
          <span className="results-count">
            Showing {persons.length} of {pagination.totalCount.toLocaleString()} persons
          </span>
          {selectedPersons.size > 0 && (
            <span className="selection-count">
              {selectedPersons.size} selected
            </span>
          )}
        </div>

        <div className="summary-center">
          {selectedPersons.size > 0 && (
            <button
              onClick={() => setShowBulkPanel(true)}
              className="btn btn-primary btn-sm"
            >
              <FiSettings />
              Bulk Actions ({selectedPersons.size})
            </button>
          )}
        </div>

        <div className="summary-right">
          <div className="view-controls">
            <button
              onClick={() => setViewMode('table')}
              className={`view-btn ${viewMode === 'table' ? 'active' : ''}`}
              title="Table View"
            >
              <FiList />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              title="Grid View"
            >
              <FiGrid />
            </button>
          </div>

          <div className="page-size-selector">
            <label>Show:</label>
            <select
              value={pagination.pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span>per page</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-error">
          {error}
          <button onClick={loadPersons} className="retry-btn">Retry</button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading persons...</p>
        </div>
      )}

      {/* Enhanced Person Table */}
      {!loading && !error && viewMode === 'table' && (
        <div className="person-table-container-enhanced">
          <table className="person-table-enhanced">
            <thead>
              <tr>
                <th className="select-column">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    ref={checkbox => {
                      if (checkbox) checkbox.indeterminate = someSelected;
                    }}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </th>
                <th
                  className="sortable"
                  onClick={() => handleSort('name')}
                >
                  Name
                  {sortConfig.field === 'name' && (
                    <span className="sort-indicator">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </th>
                <th
                  className="sortable"
                  onClick={() => handleSort('mobile')}
                >
                  <FiPhone className="header-icon" />
                  Mobile
                  {sortConfig.field === 'mobile' && (
                    <span className="sort-indicator">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </th>
                <th
                  className="sortable"
                  onClick={() => handleSort('email')}
                >
                  <FiMail className="header-icon" />
                  Email
                  {sortConfig.field === 'email' && (
                    <span className="sort-indicator">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </th>
                <th>Division</th>
                <th>Category</th>
                <th>Nature</th>
                <th
                  className="sortable"
                  onClick={() => handleSort('createdAt')}
                >
                  <FiCalendar className="header-icon" />
                  Created
                  {sortConfig.field === 'createdAt' && (
                    <span className="sort-indicator">
                      {sortConfig.direction === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </th>
                <th className="actions-column">Actions</th>
              </tr>
            </thead>
            <tbody>
              {persons.length === 0 ? (
                <tr>
                  <td colSpan="9" className="no-data">
                    <div className="empty-state">
                      <FiUsers className="empty-icon" />
                      <h3>No persons found</h3>
                      <p>Try adjusting your search criteria or add new persons</p>
                    </div>
                  </td>
                </tr>
              ) : (
                persons.map(person => (
                  <tr
                    key={person.id}
                    className={selectedPersons.has(person.id) ? 'selected' : ''}
                  >
                    <td className="select-cell">
                      <input
                        type="checkbox"
                        checked={selectedPersons.has(person.id)}
                        onChange={(e) => handleSelectPerson(person.id, e.target.checked)}
                      />
                    </td>
                    <td className="name-cell">
                      <div className="person-name">
                        <strong>{person.name}</strong>
                        {person.firmName && (
                          <div className="firm-name">{person.firmName}</div>
                        )}
                      </div>
                    </td>
                    <td className="contact-cell">
                      <div className="contact-info">
                        {person.mobileNumber ? (
                          <div className="contact-item">
                            <FiPhone className="contact-icon" />
                            <span>{person.mobileNumber}</span>
                          </div>
                        ) : (
                          <span className="no-contact">No mobile</span>
                        )}
                        {person.alternateNumbers?.length > 0 && (
                          <div className="alternate">+{person.alternateNumbers.length} more</div>
                        )}
                      </div>
                    </td>
                    <td className="email-cell">
                      <div className="email-info">
                        {person.primaryEmailId ? (
                          <div className="contact-item">
                            <FiMail className="contact-icon" />
                            <span>{person.primaryEmailId}</span>
                          </div>
                        ) : (
                          <span className="no-contact">No email</span>
                        )}
                        {person.alternateEmailIds?.length > 0 && (
                          <div className="alternate">+{person.alternateEmailIds.length} more</div>
                        )}
                      </div>
                    </td>
                    <td className="division-cell">
                      <div className="hierarchy-info">
                        <FiMapPin className="hierarchy-icon" />
                        {person.division?.name || 'N/A'}
                      </div>
                    </td>
                    <td className="category-cell">
                      <div className="hierarchy-info">
                        {person.category?.name || 'N/A'}
                        {person.subCategory && (
                          <div className="subcategory">{person.subCategory.name}</div>
                        )}
                      </div>
                    </td>
                    <td className="nature-cell">
                      <span className={`nature-badge nature-${person.nature}`}>
                        {person.natureDisplay}
                      </span>
                    </td>
                    <td className="date-cell">
                      <div className="date-info">
                        <FiCalendar className="date-icon" />
                        {formatDate(person.createdAt)}
                      </div>
                    </td>
                    <td className="actions-cell">
                      <div className="action-buttons-enhanced">
                        <button
                          onClick={() => handleViewPerson(person)}
                          className="btn-action view"
                          title="View details"
                        >
                          <FiEye />
                        </button>
                        <button
                          onClick={() => onEditPerson(person)}
                          className="btn-action edit"
                          title="Edit person"
                        >
                          <FiEdit3 />
                        </button>
                        <div className="dropdown">
                          <button className="btn-action more" title="More actions">
                            <FiMoreVertical />
                          </button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Grid View */}
      {!loading && !error && viewMode === 'grid' && (
        <div className="person-grid-container">
          <div className="person-grid">
            {persons.map(person => (
              <div
                key={person.id}
                className={`person-card ${selectedPersons.has(person.id) ? 'selected' : ''}`}
              >
                <div className="card-header">
                  <input
                    type="checkbox"
                    checked={selectedPersons.has(person.id)}
                    onChange={(e) => handleSelectPerson(person.id, e.target.checked)}
                    className="card-checkbox"
                  />
                  <div className="card-actions">
                    <button
                      onClick={() => handleViewPerson(person)}
                      className="btn-action view"
                      title="View details"
                    >
                      <FiEye />
                    </button>
                    <button
                      onClick={() => onEditPerson(person)}
                      className="btn-action edit"
                      title="Edit person"
                    >
                      <FiEdit3 />
                    </button>
                  </div>
                </div>

                <div className="card-content">
                  <h3 className="person-name">{person.name}</h3>
                  {person.firmName && (
                    <p className="firm-name">{person.firmName}</p>
                  )}

                  <div className="contact-details">
                    {person.mobileNumber && (
                      <div className="contact-item">
                        <FiPhone />
                        <span>{person.mobileNumber}</span>
                      </div>
                    )}
                    {person.primaryEmailId && (
                      <div className="contact-item">
                        <FiMail />
                        <span>{person.primaryEmailId}</span>
                      </div>
                    )}
                  </div>

                  <div className="card-meta">
                    <div className="hierarchy">
                      <FiMapPin />
                      <span>{person.division?.name} / {person.category?.name}</span>
                    </div>
                    <span className={`nature-badge nature-${person.nature}`}>
                      {person.natureDisplay}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination */}
      {!loading && !error && pagination.totalPages > 1 && (
        <div className="pagination-container">
          {renderPagination()}
        </div>
      )}

      {/* Modals */}
      {showDetailModal && selectedPerson && (
        <PersonDetailModal
          person={selectedPerson}
          onClose={() => setShowDetailModal(false)}
          onEdit={onEditPerson}
        />
      )}

      {showBulkPanel && (
        <BulkOperationsPanel
          selectedPersons={Array.from(selectedPersons)}
          onClose={() => setShowBulkPanel(false)}
          onAction={handleBulkAction}
        />
      )}

      {showExportModal && (
        <ExportModal
          onClose={() => setShowExportModal(false)}
          filters={searchFilters}
          totalCount={pagination.totalCount}
        />
      )}
    </div>
  );
};

export default PersonList;
