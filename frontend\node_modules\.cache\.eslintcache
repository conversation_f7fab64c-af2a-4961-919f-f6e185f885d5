[{"C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js": "3", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js": "6", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js": "8", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js": "9", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js": "10", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js": "11", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js": "12", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js": "13", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js": "14", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js": "15", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js": "16", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js": "17", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js": "18", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js": "19", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js": "20", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js": "21", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js": "22", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js": "23", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js": "24", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js": "25", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js": "26", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js": "27", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js": "28", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js": "29", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js": "30", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonDetailModal.js": "31", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\BulkOperationsPanel.js": "32", "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ExportModal.js": "33"}, {"size": 263, "mtime": 1753902733652, "results": "34", "hashOfConfig": "35"}, {"size": 2056, "mtime": 1753981799344, "results": "36", "hashOfConfig": "35"}, {"size": 2205, "mtime": 1753902733684, "results": "37", "hashOfConfig": "35"}, {"size": 2627, "mtime": 1753981799344, "results": "38", "hashOfConfig": "35"}, {"size": 480, "mtime": 1753902733668, "results": "39", "hashOfConfig": "35"}, {"size": 9616, "mtime": 1753902733668, "results": "40", "hashOfConfig": "35"}, {"size": 9249, "mtime": 1753902733668, "results": "41", "hashOfConfig": "35"}, {"size": 4195, "mtime": 1753902733668, "results": "42", "hashOfConfig": "35"}, {"size": 22852, "mtime": 1753915934729, "results": "43", "hashOfConfig": "35"}, {"size": 5838, "mtime": 1754012688901, "results": "44", "hashOfConfig": "35"}, {"size": 8156, "mtime": 1753982875438, "results": "45", "hashOfConfig": "35"}, {"size": 33689, "mtime": 1754014642284, "results": "46", "hashOfConfig": "35"}, {"size": 4164, "mtime": 1753902733668, "results": "47", "hashOfConfig": "35"}, {"size": 3257, "mtime": 1753902733668, "results": "48", "hashOfConfig": "35"}, {"size": 26745, "mtime": 1754015620072, "results": "49", "hashOfConfig": "35"}, {"size": 42156, "mtime": 1753960642253, "results": "50", "hashOfConfig": "35"}, {"size": 14452, "mtime": 1753954627628, "results": "51", "hashOfConfig": "35"}, {"size": 12054, "mtime": 1753963443321, "results": "52", "hashOfConfig": "35"}, {"size": 11255, "mtime": 1753981281797, "results": "53", "hashOfConfig": "35"}, {"size": 10100, "mtime": 1753909815406, "results": "54", "hashOfConfig": "35"}, {"size": 14987, "mtime": 1753963037736, "results": "55", "hashOfConfig": "35"}, {"size": 6753, "mtime": 1753910016943, "results": "56", "hashOfConfig": "35"}, {"size": 9504, "mtime": 1753963760972, "results": "57", "hashOfConfig": "35"}, {"size": 4518, "mtime": 1753960608168, "results": "58", "hashOfConfig": "35"}, {"size": 14697, "mtime": 1754012544766, "results": "59", "hashOfConfig": "35"}, {"size": 12511, "mtime": 1753958855389, "results": "60", "hashOfConfig": "35"}, {"size": 9084, "mtime": 1753947578656, "results": "61", "hashOfConfig": "35"}, {"size": 5304, "mtime": 1753944960474, "results": "62", "hashOfConfig": "35"}, {"size": 5037, "mtime": 1753962600675, "results": "63", "hashOfConfig": "35"}, {"size": 9208, "mtime": 1754012719848, "results": "64", "hashOfConfig": "35"}, {"size": 7285, "mtime": 1754016079970, "results": "65", "hashOfConfig": "35"}, {"size": 9979, "mtime": 1754015723947, "results": "66", "hashOfConfig": "35"}, {"size": 9409, "mtime": 1754015803861, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ibwsxa", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\context\\AuthContext.js", ["167"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Navbar.js", ["168"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\CategoryManagement.js", ["169"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\DivisionSetup.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonsView.js", ["170", "171", "172", "173"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonManagement.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportPersons.js", ["174", "175"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormBuilder.js", ["176"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonList.js", ["177", "178", "179", "180", "181", "182"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\DynamicPersonForm.js", ["183", "184", "185", "186", "187", "188", "189", "190", "191"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormSelectionView.js", ["192", "193", "194"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportResults.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FieldConfigModal.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\FieldMapping.js", ["195", "196"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormPreview.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\ImportProgress.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\apiService.js", ["197"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\services\\formConfigService.js", ["198", "199", "200", "201"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\constants\\personConstants.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\HierarchicalSelector.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\FormField.js", [], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\import\\DivisionCategorySelection.js", ["202", "203"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\forms\\AllFormsModal.js", ["204", "205"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\PersonDetailModal.js", ["206"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\BulkOperationsPanel.js", ["207"], [], "C:\\Users\\<USER>\\Downloads\\Advanced\\Advanced CRM filed all use\\frontend\\src\\components\\ExportModal.js", ["208"], [], {"ruleId": "209", "severity": 1, "message": "210", "line": 26, "column": 6, "nodeType": "211", "endLine": 26, "endColumn": 13, "suggestions": "212"}, {"ruleId": "213", "severity": 1, "message": "214", "line": 4, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 4, "endColumn": 16}, {"ruleId": "213", "severity": 1, "message": "217", "line": 11, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 11, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "218", "line": 15, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 15, "endColumn": 13}, {"ruleId": "213", "severity": 1, "message": "219", "line": 27, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 27, "endColumn": 20}, {"ruleId": "209", "severity": 1, "message": "220", "line": 63, "column": 6, "nodeType": "211", "endLine": 63, "endColumn": 57, "suggestions": "221"}, {"ruleId": "213", "severity": 1, "message": "222", "line": 280, "column": 9, "nodeType": "215", "messageId": "216", "endLine": 280, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "223", "line": 15, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 15, "endColumn": 22}, {"ruleId": "213", "severity": 1, "message": "224", "line": 24, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 24, "endColumn": 17}, {"ruleId": "209", "severity": 1, "message": "225", "line": 44, "column": 6, "nodeType": "211", "endLine": 44, "endColumn": 21, "suggestions": "226"}, {"ruleId": "213", "severity": 1, "message": "227", "line": 1, "column": 51, "nodeType": "215", "messageId": "216", "endLine": 1, "endColumn": 58}, {"ruleId": "213", "severity": 1, "message": "228", "line": 5, "column": 44, "nodeType": "215", "messageId": "216", "endLine": 5, "endColumn": 57}, {"ruleId": "213", "severity": 1, "message": "229", "line": 5, "column": 59, "nodeType": "215", "messageId": "216", "endLine": 5, "endColumn": 67}, {"ruleId": "213", "severity": 1, "message": "230", "line": 47, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 47, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "231", "line": 47, "column": 25, "nodeType": "215", "messageId": "216", "endLine": 47, "endColumn": 41}, {"ruleId": "209", "severity": 1, "message": "220", "line": 57, "column": 6, "nodeType": "211", "endLine": 57, "endColumn": 56, "suggestions": "232"}, {"ruleId": "213", "severity": 1, "message": "233", "line": 2, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 28}, {"ruleId": "213", "severity": 1, "message": "234", "line": 2, "column": 30, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 42}, {"ruleId": "213", "severity": 1, "message": "235", "line": 2, "column": 44, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 64}, {"ruleId": "209", "severity": 1, "message": "236", "line": 136, "column": 6, "nodeType": "211", "endLine": 136, "endColumn": 24, "suggestions": "237"}, {"ruleId": "209", "severity": 1, "message": "238", "line": 143, "column": 6, "nodeType": "211", "endLine": 143, "endColumn": 27, "suggestions": "239"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 364, "column": 35, "nodeType": "242", "messageId": "243", "endLine": 364, "endColumn": 36, "suggestions": "244"}, {"ruleId": "245", "severity": 1, "message": "246", "line": 404, "column": 9, "nodeType": "247", "messageId": "248", "endLine": 435, "endColumn": 10}, {"ruleId": "240", "severity": 1, "message": "241", "line": 412, "column": 40, "nodeType": "242", "messageId": "243", "endLine": 412, "endColumn": 41, "suggestions": "249"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 529, "column": 37, "nodeType": "242", "messageId": "243", "endLine": 529, "endColumn": 38, "suggestions": "250"}, {"ruleId": "213", "severity": 1, "message": "217", "line": 9, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 9, "endColumn": 23}, {"ruleId": "209", "severity": 1, "message": "251", "line": 28, "column": 6, "nodeType": "211", "endLine": 28, "endColumn": 8, "suggestions": "252"}, {"ruleId": "213", "severity": 1, "message": "253", "line": 163, "column": 9, "nodeType": "215", "messageId": "216", "endLine": 163, "endColumn": 31}, {"ruleId": "209", "severity": 1, "message": "254", "line": 22, "column": 6, "nodeType": "211", "endLine": 22, "endColumn": 19, "suggestions": "255"}, {"ruleId": "209", "severity": 1, "message": "256", "line": 27, "column": 6, "nodeType": "211", "endLine": 27, "endColumn": 21, "suggestions": "257"}, {"ruleId": "258", "severity": 1, "message": "259", "line": 192, "column": 1, "nodeType": "260", "endLine": 192, "endColumn": 33}, {"ruleId": "261", "severity": 1, "message": "262", "line": 36, "column": 9, "nodeType": "263", "messageId": "264", "endLine": 36, "endColumn": 15}, {"ruleId": "265", "severity": 1, "message": "266", "line": 467, "column": 3, "nodeType": "267", "messageId": "264", "endLine": 476, "endColumn": 4}, {"ruleId": "265", "severity": 1, "message": "268", "line": 479, "column": 3, "nodeType": "267", "messageId": "264", "endLine": 495, "endColumn": 4}, {"ruleId": "258", "severity": 1, "message": "259", "line": 498, "column": 1, "nodeType": "260", "endLine": 498, "endColumn": 40}, {"ruleId": "213", "severity": 1, "message": "269", "line": 1, "column": 27, "nodeType": "215", "messageId": "216", "endLine": 1, "endColumn": 36}, {"ruleId": "213", "severity": 1, "message": "270", "line": 11, "column": 21, "nodeType": "215", "messageId": "216", "endLine": 11, "endColumn": 33}, {"ruleId": "213", "severity": 1, "message": "271", "line": 8, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 8, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "272", "line": 9, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 9, "endColumn": 20}, {"ruleId": "213", "severity": 1, "message": "273", "line": 3, "column": 26, "nodeType": "215", "messageId": "216", "endLine": 3, "endColumn": 32}, {"ruleId": "213", "severity": 1, "message": "274", "line": 3, "column": 8, "nodeType": "215", "messageId": "216", "endLine": 3, "endColumn": 15}, {"ruleId": "213", "severity": 1, "message": "275", "line": 4, "column": 15, "nodeType": "215", "messageId": "216", "endLine": 4, "endColumn": 22}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateToken'. Either include it or remove the dependency array.", "ArrayExpression", ["276"], "no-unused-vars", "'FiHome' is defined but never used.", "Identifier", "unusedVar", "'subCategories' is assigned a value but never used.", "'FiBuilding' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPersons'. Either include it or remove the dependency array.", ["277"], "'getGenderLabel' is assigned a value but never used.", "'fieldMapping' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadInitialConfig'. Either include it or remove the dependency array.", ["278"], "'useMemo' is defined but never used.", "'FiCheckSquare' is defined but never used.", "'FiSquare' is defined but never used.", "'savedSearches' is assigned a value but never used.", "'setSavedSearches' is assigned a value but never used.", ["279"], "'PersonNatureLabels' is defined but never used.", "'GenderLabels' is defined but never used.", "'WorkingProfileLabels' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleCategorySelection'. Either include it or remove the dependency array.", ["280"], "React Hook useEffect has a missing dependency: 'handleSubCategorySelection'. Either include it or remove the dependency array.", ["281"], "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["282", "283"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["284", "285"], ["286", "287"], "React Hook useEffect has a missing dependency: 'loadAvailableForms'. Either include it or remove the dependency array.", ["288"], "'getFormsForSubCategory' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'autoMapFields', 'updateRequiredFieldsStatus', and 'updateUnmappedHeaders'. Either include them or remove the dependency array.", ["289"], "React Hook useEffect has missing dependencies: 'mapping' and 'updateRequiredFieldsStatus'. Either include them or remove the dependency array.", ["290"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-dupe-keys", "Duplicate key 'fields'.", "ObjectExpression", "unexpected", "no-dupe-class-members", "Duplicate name 'deleteFormConfig'.", "MethodDefinition", "Duplicate name 'clearAllFormConfigs'.", "'useEffect' is defined but never used.", "'setIsLoading' is assigned a value but never used.", "'divisions' is assigned a value but never used.", "'categories' is assigned a value but never used.", "'FiMail' is defined but never used.", "'FiEdit3' is defined but never used.", "'FiCheck' is defined but never used.", {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"messageId": "303", "fix": "304", "desc": "305"}, {"messageId": "306", "fix": "307", "desc": "308"}, {"messageId": "303", "fix": "309", "desc": "305"}, {"messageId": "306", "fix": "310", "desc": "308"}, {"messageId": "303", "fix": "311", "desc": "305"}, {"messageId": "306", "fix": "312", "desc": "308"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, {"desc": "317", "fix": "318"}, "Update the dependencies array to be: [token, validateToken]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [initialConfig, loadInitialConfig]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [loadPersons, pagination.page, pagination.pageSize, sortConfig]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [handleCategorySelection, selectedCategory]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [handleSubCategorySelection, selectedSubCategory]", {"range": "329", "text": "330"}, "removeEscape", {"range": "331", "text": "332"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "333", "text": "334"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "335", "text": "332"}, {"range": "336", "text": "334"}, {"range": "337", "text": "332"}, {"range": "338", "text": "334"}, "Update the dependencies array to be: [loadAvailableForms]", {"range": "339", "text": "340"}, "Update the dependencies array to be: [autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", {"range": "341", "text": "342"}, "Update the dependencies array to be: [defaultValues, mapping, updateRequiredFieldsStatus]", {"range": "343", "text": "344"}, [747, 754], "[token, validateToken]", [1595, 1646], "[currentPage, pageSize, filters, sortBy, sortOrder, loadPersons]", [1878, 1893], "[initialConfig, loadInitialConfig]", [1999, 2049], "[loadPersons, pagination.page, pagination.pageSize, sortConfig]", [4245, 4263], "[handleCategorySelection, selectedCategory]", [4435, 4456], "[handleSubCategorySelection, selectedSubCategory]", [11986, 11987], "", [11986, 11986], "\\", [13697, 13698], [13697, 13697], [19152, 19153], [19152, 19152], [1121, 1123], "[loadAvailableForms]", [961, 974], "[autoMapFields, fileHeaders, updateRequiredFieldsStatus, updateUnmappedHeaders]", [1088, 1103], "[defaultValues, mapping, updateRequiredFieldsStatus]"]